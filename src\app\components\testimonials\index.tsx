"use client";

import React, { useEffect, useState } from "react";
import Image from "next/image";

import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

import Rating from "../rating";
import { testimonialAssets, testimonials } from "./testimonialDetails";

const Testimonials = () => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Slider settings
  const sliderSettings = {
    dots: false,
    infinite: true,
    speed: 2000,
    slidesToShow: 3,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 5000,
    className: "testimonial_slider",
    adaptiveHeight: true,
    pauseOnHover: true,
    swipe: true,
    touchMove: true,
    draggable: true,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
          infinite: true,
          dots: false,
          adaptiveHeight: true,
        },
      },
      {
        breakpoint: 640,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          infinite: true,
          dots: false,
          adaptiveHeight: true,
        },
      },
    ],
  };

  // Don't render slider on server side to avoid hydration issues
  if (!isClient) {
    return (
      <>
        <figure className="testimonial_ab_fig">
          <Image
            src={testimonialAssets?.backgroundImage}
            alt="Testimonials background"
            width={1521}
            height={280}
            className="object-cover"
          />
        </figure>

        <section className="testimonial_section">
          <div className="testimonial_heading">
            <small>
              <strong></strong> <span></span>
              <span></span>
              <span></span>
            </small>
            <h3>{"Find Out Why We're Adored"}</h3>
            <small>
              {" "}
              <span></span>
              <span></span>
              <span></span>
              <strong></strong>
            </small>
          </div>

          <div className="custom_container">
            <div className="testimonial_main">
              <div className="testimonial_list">
                <div className="testimonial_slider">
                  {testimonials?.slice(0, 3)?.map((testimonial) => (
                    <div key={testimonial.id} className="testimonial_slide">
                      <div className="testimonial_data_box">
                        <div className="testimonial_review_data_main">
                          <div className="testimonial_description">
                            <p style={{ height: "152px", overflow: "auto" }}>
                              {testimonial?.review}
                            </p>
                          </div>

                          <div className="testimonial_figure_box">
                            <figure>
                              <Image
                                src={testimonial?.image}
                                alt={`${testimonial?.name} - Customer`}
                                width={80}
                                height={80}
                                className="rounded-full"
                              />
                            </figure>
                          </div>
                        </div>

                        <div className="testimonial_review_data_main-second">
                          <div className="testimonial_rating_stars">
                            <Rating rating={testimonial?.rating || 0} />
                          </div>

                          <div className="testimonial_reviewer_data">
                            <h4>{testimonial?.name}</h4>

                            <span>
                              {testimonial?.role}, {testimonial.location}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </section>
      </>
    );
  }

  return (
    <>
      <figure className="testimonial_ab_fig">
        <Image
          src={testimonialAssets?.backgroundImage}
          alt="Testimonials background"
          width={1521}
          height={280}
          className="object-cover"
        />
      </figure>

      <section className="testimonial_section">
        <div className="testimonial_heading">
          <small>
            <strong></strong> <span></span>
            <span></span>
            <span></span>
          </small>
          <h3>{"Find Out Why We're Adored"}</h3>
          <small>
            {" "}
            <span></span>
            <span></span>
            <span></span>
            <strong></strong>
          </small>
        </div>

        <div className="custom_container">
          <div className="testimonial_main">
            <div className="testimonial_list">
              <Slider {...sliderSettings}>
                {testimonials?.map((testimonial) => (
                  <div key={testimonial.id}>
                    <div className="testimonial_data_box">
                      <div className="testimonial_review_data_main">
                        <div className="testimonial_description">
                          <p style={{ height: "152px", overflow: "auto" }}>
                            {testimonial?.review}
                          </p>
                        </div>

                        <div className="testimonial_figure_box">
                          <figure>
                            <Image
                              src={testimonial?.image}
                              alt={`${testimonial?.name} - Customer`}
                              width={80}
                              height={80}
                              className="rounded-full"
                            />
                          </figure>
                        </div>
                      </div>

                      <div className="testimonial_review_data_main-second">
                        <div className="testimonial_rating_stars">
                          <Rating rating={testimonial?.rating || 0} />
                        </div>

                        <div className="testimonial_reviewer_data">
                          <h4>{testimonial?.name}</h4>

                          <span>
                            {testimonial?.role}, {testimonial.location}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </Slider>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Testimonials;
