"use client";

import React from "react";
import Image from "next/image";

import Slider from "react-slick";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";

import Rating from "../rating";
import { testimonialAssets, testimonials } from "./testimonialDetails";

const Testimonials = () => {
  // slider base settings
  const sliderBaseSettings = {
    dots: false,
    infinite: true,
    speed: 2000,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 5000,
    className: "testimonial_slider",
    adaptiveHeight: true,
    pauseOnHover: true,
    swipe: true,
  };

  // ==============================|| UI ||============================== //

  // function call to render testimonial content
  const renderTestimonials = (): React.JSX.Element[] => {
    return testimonials?.map((testimonial) => (
      <div key={testimonial.id} className="testimonial_list_content">
        <div className="testimonial_data_box">
          <div className="testimonial_review_data_main">
            <div className="testimonial_description">
              <p className="h-[152px] overflow-auto">{testimonial?.review}</p>
            </div>

            <div className="testimonial_figure_box">
              <figure>
                <Image
                  src={testimonial?.image}
                  alt={`${testimonial?.name} - Customer`}
                  width={80}
                  height={80}
                  className="rounded-full"
                />
              </figure>
            </div>
          </div>

          <div className="testimonial_review_data_main-second">
            <div className="testimonial_rating_stars">
              <Rating rating={testimonial?.rating || 0} />
            </div>

            <div className="testimonial_reviewer_data">
              <h4>{testimonial?.name}</h4>

              <span>
                {testimonial?.role}, {testimonial?.location}
              </span>
            </div>
          </div>
        </div>
      </div>
    ));
  };

  return (
    <>
      <figure className="testimonial_ab_fig">
        <Image
          src={testimonialAssets?.backgroundImage}
          alt="Testimonials background"
          width={1521}
          height={280}
          className="object-cover"
        />
      </figure>

      <section className="testimonial_section">
        <div className="testimonial_heading">
          <small>
            <strong></strong> <span></span>
            <span></span>
            <span></span>
          </small>

          <h3>{"Find Out Why We're Adored"}</h3>

          <small>
            {" "}
            <span></span>
            <span></span>
            <span></span>
            <strong></strong>
          </small>
        </div>

        <div className="custom_container">
          <div className="testimonial_main">
            <div className="testimonial_list">
              {/* desktop slider - 3 slides - visible on lg and above */}
              <div className="hidden lg:block">
                <Slider {...sliderBaseSettings} slidesToShow={3}>
                  {renderTestimonials()}
                </Slider>
              </div>

              {/* tablet slider - 2 slides - visible on md to lg */}
              <div className="hidden md:block lg:hidden">
                <Slider {...sliderBaseSettings} slidesToShow={2}>
                  {renderTestimonials()}
                </Slider>
              </div>

              {/* mobile slider - 1 slide - visible on sm and below */}
              <div className="block md:hidden">
                <Slider {...sliderBaseSettings} slidesToShow={1}>
                  {renderTestimonials()}
                </Slider>
              </div>
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default Testimonials;
